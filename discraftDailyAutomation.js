import { createClient } from '@supabase/supabase-js';
import nodemailer from 'nodemailer';
import fetch from 'node-fetch';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Initialize Supabase client
const supabaseUrl = 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMzc4NjU0MSwiZXhwIjoyMDQ5MzYyNTQxfQ.FQyPTgdBP83VhuykdlZkagHADc5nBmx7-yd0JYt5aP8';
const supabase = createClient(supabaseUrl, supabaseKey);

// Load environment variables
import dotenv from 'dotenv';
dotenv.config();

// Email configuration
const emailConfig = {
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    auth: {
        user: process.env.EMAIL_USER || '<EMAIL>',
        pass: process.env.EMAIL_PASS || 'your-app-password'
    }
};

async function downloadDiscraftFile() {
    console.log('📥 Downloading Discraft vendor file...');
    
    try {
        const vendorUrl = 'https://www.discgolf.discraft.com/forms/stock.xlsx';
        const outputPath = path.join(__dirname, 'data', 'external data', 'discraftstock.xlsx');
        
        // Download the file
        const response = await fetch(vendorUrl);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const buffer = await response.buffer();
        
        // Ensure directory exists
        const dir = path.dirname(outputPath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        
        // Write the file
        fs.writeFileSync(outputPath, buffer);
        
        console.log(`✅ Downloaded ${buffer.length} bytes to ${outputPath}`);
        return { success: true, filePath: outputPath, fileSize: buffer.length };
        
    } catch (error) {
        console.error('❌ Download failed:', error);
        return { success: false, error: error.message };
    }
}

async function runDiscraftImport() {
    console.log('🔄 Running Discraft import...');

    try {
        // Make API call to run import
        const response = await fetch('http://localhost:3001/api/discraft/import', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        });

        const result = await response.json();
        console.log('✅ Import completed:', result);
        return result;

    } catch (error) {
        console.error('❌ Import failed:', error);
        return { success: false, error: error.message };
    }
}

async function calculateMpsMatching() {
    console.log('🎯 Calculating MPS matching...');
    
    try {
        // Make API call to calculate matching
        const response = await fetch('http://localhost:3001/api/discraft/calculate-mps', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        });
        
        const result = await response.json();
        console.log('✅ MPS matching completed:', result);
        return result;
        
    } catch (error) {
        console.error('❌ MPS matching failed:', error);
        return { success: false, error: error.message };
    }
}

async function getOrderSummary() {
    console.log('📊 Calculating order summary...');
    
    try {
        // Query the view to get order totals with Excel mapping data
        const { data, error } = await supabase
            .from('v_stats_by_osl_discraft')
            .select('order, mold_name, plastic_name, is_currently_available, excel_mapping_key, excel_column, excel_row_hint');
        
        if (error) {
            throw new Error(error.message);
        }

        // Filter for items with order > 0
        const orderableData = data.filter(item => (item.order || 0) > 0);

        const totalOrderQuantity = orderableData.reduce((sum, item) => sum + (item.order || 0), 0);
        const uniqueMolds = [...new Set(orderableData.map(item => item.mold_name))].length;
        const uniquePlastics = [...new Set(orderableData.map(item => item.plastic_name))].length;

        // Count records with Excel mapping for export consistency tracking
        const recordsWithMapping = orderableData.filter(item => item.excel_row_hint && item.excel_column);
        const exportableQuantity = recordsWithMapping.reduce((sum, item) => sum + (item.order || 0), 0);

        console.log(`📊 Order Summary Details:`);
        console.log(`   Total records with order > 0: ${orderableData.length}`);
        console.log(`   Total order quantity (EMAIL): ${totalOrderQuantity} discs`);
        console.log(`   Records with Excel mapping: ${recordsWithMapping.length}`);
        console.log(`   Exportable quantity (EXCEL): ${exportableQuantity} discs`);
        if (totalOrderQuantity !== exportableQuantity) {
            console.log(`   ⚠️  DISCREPANCY: ${totalOrderQuantity - exportableQuantity} discs missing Excel mapping`);
        }

        // Group by mold for summary
        const moldSummary = orderableData.reduce((acc, item) => {
            if (!acc[item.mold_name]) {
                acc[item.mold_name] = 0;
            }
            acc[item.mold_name] += item.order;
            return acc;
        }, {});
        
        // Top 10 molds by order quantity
        const topMolds = Object.entries(moldSummary)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10);
        
        console.log(`✅ Order summary: ${totalOrderQuantity} total discs, ${uniqueMolds} molds, ${uniquePlastics} plastics`);
        
        return {
            success: true,
            totalOrderQuantity,
            uniqueMolds,
            uniquePlastics,
            topMolds,
            detailedData: orderableData,
            shouldExport: totalOrderQuantity >= (process.env.DISCRAFT_EXPORT_THRESHOLD || 100)
        };
        
    } catch (error) {
        console.error('❌ Order summary failed:', error);
        return { success: false, error: error.message };
    }
}

async function sendEmailReport(downloadResult, importResult, matchingResult, orderSummary, exportResult) {
    console.log('📧 Sending email report...');

    try {
        // Check if email is configured
        if (!process.env.EMAIL_USER || process.env.EMAIL_USER === '<EMAIL>') {
            console.log('📧 Email not configured, printing report to console instead...');
            printReportToConsole(downloadResult, importResult, matchingResult, orderSummary, exportResult);
            return { success: true, message: 'Report printed to console (email not configured)' };
        }

        const transporter = nodemailer.createTransport(emailConfig);
        
        const currentDate = new Date().toLocaleDateString('en-US', {
            timeZone: 'America/Chicago',
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
        
        const currentTime = new Date().toLocaleTimeString('en-US', {
            timeZone: 'America/Chicago',
            hour: '2-digit',
            minute: '2-digit'
        });
        
        let emailBody = `
<h2>📊 Daily Discraft Order Report - ${currentDate}</h2>
<p><strong>Generated:</strong> ${currentTime} CST</p>

<h3>🎯 Order Summary</h3>
`;
        
        if (orderSummary.success) {
            emailBody += `
<div style="background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0;">
    <h4 style="color: #2d5a2d; margin: 0 0 10px 0;">📦 Total Order Quantity: ${orderSummary.totalOrderQuantity} discs</h4>
    <p><strong>Unique Molds:</strong> ${orderSummary.uniqueMolds} | <strong>Unique Plastics:</strong> ${orderSummary.uniquePlastics}</p>
</div>

<h4>🏆 Top 10 Molds to Order:</h4>
<table style="border-collapse: collapse; width: 100%; margin: 10px 0;">
    <tr style="background: #f0f0f0;">
        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Mold</th>
        <th style="border: 1px solid #ddd; padding: 8px; text-align: right;">Quantity</th>
    </tr>
`;
            
            orderSummary.topMolds.forEach(([mold, quantity]) => {
                emailBody += `
    <tr>
        <td style="border: 1px solid #ddd; padding: 8px;">${mold}</td>
        <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${quantity}</td>
    </tr>`;
            });
            
            emailBody += `</table>`;

            // Add export information
            if (exportResult && exportResult.exported) {
                emailBody += `
<div style="background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 15px 0;">
    <h4 style="color: #1e4d72; margin: 0 0 10px 0;">📄 Order Sheet Exported</h4>
    <p><strong>File:</strong> ${exportResult.filename}</p>
    <p><strong>Records:</strong> ${exportResult.totalRecords} products with order quantities</p>
    <p style="margin: 0;"><em>Excel file is attached - ready to forward to Discraft!</em></p>
</div>`;
            } else if (exportResult && exportResult.skipped) {
                emailBody += `
<div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;">
    <h4 style="color: #856404; margin: 0 0 10px 0;">📄 Export Skipped</h4>
    <p>Order quantity (${orderSummary.totalOrderQuantity}) below export threshold (${process.env.DISCRAFT_EXPORT_THRESHOLD || 100} discs)</p>
</div>`;
            }
        } else {
            emailBody += `<p style="color: red;">❌ Order calculation failed: ${orderSummary.error}</p>`;
        }
        
        emailBody += `
<h3>🔄 Process Status</h3>
<ul>
    <li><strong>Download:</strong> ${downloadResult.success ? '✅ Success' : '❌ Failed - ' + downloadResult.error}</li>
    <li><strong>Import:</strong> ${importResult.success ? '✅ Success' : '❌ Failed - ' + importResult.error}</li>
    <li><strong>Matching:</strong> ${matchingResult.success ? '✅ Success' : '❌ Failed - ' + matchingResult.error}</li>
    <li><strong>Order Calculation:</strong> ${orderSummary.success ? '✅ Success' : '❌ Failed - ' + orderSummary.error}</li>
</ul>

<p><small>This report was automatically generated by the Discraft Daily Automation system.</small></p>
`;
        
        const mailOptions = {
            from: emailConfig.auth.user,
            to: '<EMAIL>',
            subject: `📊 Daily Discraft Order Report - ${orderSummary.success ? orderSummary.totalOrderQuantity + ' discs' : 'Error'}`,
            html: emailBody
        };

        // Add attachment if export was successful
        if (exportResult && exportResult.exported && exportResult.filePath) {
            mailOptions.attachments = [{
                filename: exportResult.filename,
                path: exportResult.filePath
            }];
        }
        
        await transporter.sendMail(mailOptions);
        console.log('✅ Email sent successfully');
        return { success: true };
        
    } catch (error) {
        console.error('❌ Email failed:', error);
        return { success: false, error: error.message };
    }
}

async function exportOrderSheet(orderSummary) {
    console.log('📄 Exporting order sheet...');

    try {
        if (!orderSummary.shouldExport) {
            console.log(`📄 Export skipped: Order quantity ${orderSummary.totalOrderQuantity} below threshold`);
            return { success: true, skipped: true, reason: 'Below threshold' };
        }

        // Calculate export quantity from the same data used for email
        const exportData = orderSummary.detailedData.filter(item => item.excel_row_hint && item.excel_column);
        const exportQuantity = exportData.reduce((sum, item) => sum + (item.order || 0), 0);

        console.log(`📄 Export Details:`);
        console.log(`   Using ${orderSummary.detailedData.length} total records from order summary`);
        console.log(`   Filtering to ${exportData.length} records with Excel mapping`);
        console.log(`   Export quantity: ${exportQuantity} discs`);

        // Make API call to export with the same order data used for email calculation
        const response = await fetch('http://localhost:3001/api/discraft/export', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                includeHeader: true,
                filename: `discraft_order_${new Date().toISOString().replace(/T/, '-').replace(/:/g, '-').split('.')[0]}.xlsx`,
                orderData: orderSummary.detailedData // Pass the same data used for email calculation
            })
        });

        if (!response.ok) {
            throw new Error(`Export API returned ${response.status}`);
        }

        const result = await response.json();
        console.log('✅ Export completed:', result);

        return {
            success: true,
            exported: true,
            filename: result.filename,
            filePath: result.filePath,
            totalRecords: result.totalRecords
        };

    } catch (error) {
        console.error('❌ Export failed:', error);
        return { success: false, error: error.message };
    }
}

function printReportToConsole(downloadResult, importResult, matchingResult, orderSummary) {
    const currentDate = new Date().toLocaleDateString('en-US', {
        timeZone: 'America/Chicago',
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    const currentTime = new Date().toLocaleTimeString('en-US', {
        timeZone: 'America/Chicago',
        hour: '2-digit',
        minute: '2-digit'
    });

    console.log('\n' + '='.repeat(60));
    console.log(`📊 DAILY DISCRAFT ORDER REPORT - ${currentDate}`);
    console.log(`Generated: ${currentTime} CST`);
    console.log('='.repeat(60));

    if (orderSummary.success) {
        console.log(`\n🎯 ORDER SUMMARY`);
        console.log(`📦 Total Order Quantity: ${orderSummary.totalOrderQuantity} discs`);
        console.log(`🥏 Unique Molds: ${orderSummary.uniqueMolds} | 🎨 Unique Plastics: ${orderSummary.uniquePlastics}`);

        console.log(`\n🏆 TOP 10 MOLDS TO ORDER:`);
        orderSummary.topMolds.forEach(([mold, quantity], index) => {
            console.log(`   ${index + 1}. ${mold}: ${quantity} discs`);
        });
    } else {
        console.log(`\n❌ Order calculation failed: ${orderSummary.error}`);
    }

    console.log(`\n🔄 PROCESS STATUS:`);
    console.log(`   Download: ${downloadResult.success ? '✅ Success' : '❌ Failed - ' + downloadResult.error}`);
    console.log(`   Import: ${importResult.success ? '✅ Success' : '❌ Failed - ' + importResult.error}`);
    console.log(`   Matching: ${matchingResult.success ? '✅ Success' : '❌ Failed - ' + matchingResult.error}`);
    console.log(`   Order Calculation: ${orderSummary.success ? '✅ Success' : '❌ Failed - ' + orderSummary.error}`);

    console.log('\n📧 Email not configured - configure EMAIL_USER and EMAIL_PASS in .env to receive email reports');
    console.log('='.repeat(60) + '\n');
}

async function runDailyAutomation() {
    console.log('🚀 Starting Discraft Daily Automation...');
    console.log(`📅 ${new Date().toLocaleString('en-US', { timeZone: 'America/Chicago' })} CST\n`);
    
    // Step 1: Download vendor file
    const downloadResult = await downloadDiscraftFile();
    
    // Step 2: Run import (only if download succeeded)
    let importResult = { success: false, error: 'Download failed' };
    if (downloadResult.success) {
        importResult = await runDiscraftImport();
    }
    
    // Step 3: Calculate MPS matching (only if import succeeded)
    let matchingResult = { success: false, error: 'Import failed' };
    if (importResult.success) {
        matchingResult = await calculateMpsMatching();
    }
    
    // Step 4: Get order summary (always try, even if previous steps failed)
    const orderSummary = await getOrderSummary();

    // Step 5: Export order sheet if needed (pass the same order data to ensure consistency)
    let exportResult = { success: false, skipped: true, reason: 'Order summary failed' };
    if (orderSummary.success) {
        exportResult = await exportOrderSheet(orderSummary);
    }

    // Step 6: Send email report
    const emailResult = await sendEmailReport(downloadResult, importResult, matchingResult, orderSummary, exportResult);
    
    console.log('\n📊 Daily Automation Summary:');
    console.log(`   Download: ${downloadResult.success ? '✅' : '❌'}`);
    console.log(`   Import: ${importResult.success ? '✅' : '❌'}`);
    console.log(`   Matching: ${matchingResult.success ? '✅' : '❌'}`);
    console.log(`   Order Summary: ${orderSummary.success ? '✅' : '❌'}`);
    console.log(`   Export: ${exportResult.exported ? '✅' : exportResult.skipped ? '⏭️' : '❌'}`);
    console.log(`   Email: ${emailResult.success ? '✅' : '❌'}`);
    
    if (orderSummary.success) {
        console.log(`\n🎯 Total Order Quantity: ${orderSummary.totalOrderQuantity} discs`);
    }
    
    console.log('\n🏁 Daily automation completed!');
}

// Export for use in scheduler
export default runDailyAutomation;

// Run immediately if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runDailyAutomation().catch(console.error);
}
