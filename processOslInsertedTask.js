// processOslInsertedTask.js - Process osl_inserted tasks
import { createClient } from '@supabase/supabase-js';

// Function to process an osl_inserted task
export default async function processOslInsertedTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processOslInsertedTask.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[processOslInsertedTask.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[processOslInsertedTask.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[processOslInsertedTask.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        // Parse JSON string
        console.log(`[processOslInsertedTask.js] Task ${task.id} payload is a string, parsing as JSON`);
        payload = JSON.parse(task.payload);
      } else {
        throw new Error(`Unexpected payload format: ${typeof task.payload}`);
      }
    } catch (err) {
      const errMsg = `[processOslInsertedTask.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process OSL inserted task. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[processOslInsertedTask.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process OSL inserted task. Missing OSL id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const oslId = payload.id;
    console.log(`[processOslInsertedTask.js] Processing OSL inserted for id=${oslId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Create child tasks
    const tasks = [];
    const taskErrors = [];

    // 1. Create osl_inserted_create_inv_osl task
    try {
      console.log(`[processOslInsertedTask.js] Creating osl_inserted_create_inv_osl task for OSL id=${oslId}`);
      
      const { data: invOslTask, error: invOslError } = await supabase
        .from('t_task_queue')
        .insert({
          task_type: 'osl_inserted_create_inv_osl',
          payload: { id: oslId },
          status: 'pending',
          scheduled_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          enqueued_by: task.task_type
        })
        .select();

      if (invOslError) {
        const errMsg = `[processOslInsertedTask.js] Error creating osl_inserted_create_inv_osl task: ${invOslError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Creating task for OSL id=${oslId}`);
        taskErrors.push({
          task_type: 'osl_inserted_create_inv_osl',
          error: invOslError.message
        });
      } else {
        tasks.push({
          task_type: 'osl_inserted_create_inv_osl',
          task_id: invOslTask[0].id
        });
      }
    } catch (err) {
      const errMsg = `[processOslInsertedTask.js] Exception creating osl_inserted_create_inv_osl task: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Creating task for OSL id=${oslId}`);
      taskErrors.push({
        task_type: 'osl_inserted_create_inv_osl',
        error: err.message
      });
    }

    // 2. Create generate_osl_fields task with a 15-second delay
    try {
      console.log(`[processOslInsertedTask.js] Creating generate_osl_fields task for OSL id=${oslId}`);

      // Calculate scheduled time (15 seconds from now)
      const scheduledAt = new Date();
      scheduledAt.setSeconds(scheduledAt.getSeconds() + 15);

      const { data: generateTask, error: generateError } = await supabase
        .from('t_task_queue')
        .insert({
          task_type: 'generate_osl_fields',
          payload: { id: oslId },
          status: 'pending',
          scheduled_at: scheduledAt.toISOString(),
          created_at: new Date().toISOString(),
          enqueued_by: task.task_type
        })
        .select();

      if (generateError) {
        const errMsg = `[processOslInsertedTask.js] Error creating generate_osl_fields task: ${generateError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Creating task for OSL id=${oslId}`);
        taskErrors.push({
          task_type: 'generate_osl_fields',
          error: generateError.message
        });
      } else {
        tasks.push({
          task_type: 'generate_osl_fields',
          task_id: generateTask[0].id
        });
      }
    } catch (err) {
      const errMsg = `[processOslInsertedTask.js] Exception creating generate_osl_fields task: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Creating task for OSL id=${oslId}`);
      taskErrors.push({
        task_type: 'generate_osl_fields',
        error: err.message
      });
    }

    // 3. Create match_osl_to_discs task with a 30-second delay
    try {
      console.log(`[processOslInsertedTask.js] Creating match_osl_to_discs task for OSL id=${oslId}`);

      // Calculate scheduled time (30 seconds from now)
      const scheduledAt = new Date();
      scheduledAt.setSeconds(scheduledAt.getSeconds() + 30);

      const { data: matchTask, error: matchError } = await supabase
        .from('t_task_queue')
        .insert({
          task_type: 'match_osl_to_discs',
          payload: { id: oslId },
          status: 'pending',
          scheduled_at: scheduledAt.toISOString(),
          created_at: new Date().toISOString(),
          enqueued_by: task.task_type
        })
        .select();

      if (matchError) {
        const errMsg = `[processOslInsertedTask.js] Error creating match_osl_to_discs task: ${matchError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Creating task for OSL id=${oslId}`);
        taskErrors.push({
          task_type: 'match_osl_to_discs',
          error: matchError.message
        });
      } else {
        tasks.push({
          task_type: 'match_osl_to_discs',
          task_id: matchTask[0].id
        });
      }
    } catch (err) {
      const errMsg = `[processOslInsertedTask.js] Exception creating match_osl_to_discs task: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Creating task for OSL id=${oslId}`);
      taskErrors.push({
        task_type: 'match_osl_to_discs',
        error: err.message
      });
    }

    // 4. Create check_if_osl_is_ready task with a 45-second delay
    try {
      console.log(`[processOslInsertedTask.js] Creating check_if_osl_is_ready task for OSL id=${oslId}`);

      // Calculate scheduled time (45 seconds from now)
      const scheduledAt = new Date();
      scheduledAt.setSeconds(scheduledAt.getSeconds() + 45);

      const { data: checkReadyTask, error: checkReadyError } = await supabase
        .from('t_task_queue')
        .insert({
          task_type: 'check_if_osl_is_ready',
          payload: { id: oslId },
          status: 'pending',
          scheduled_at: scheduledAt.toISOString(),
          created_at: new Date().toISOString(),
          enqueued_by: task.task_type
        })
        .select();

      if (checkReadyError) {
        const errMsg = `[processOslInsertedTask.js] Error creating check_if_osl_is_ready task: ${checkReadyError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Creating task for OSL id=${oslId}`);
        taskErrors.push({
          task_type: 'check_if_osl_is_ready',
          error: checkReadyError.message
        });
      } else {
        tasks.push({
          task_type: 'check_if_osl_is_ready',
          task_id: checkReadyTask[0].id
        });
      }
    } catch (err) {
      const errMsg = `[processOslInsertedTask.js] Exception creating check_if_osl_is_ready task: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Creating task for OSL id=${oslId}`);
      taskErrors.push({
        task_type: 'check_if_osl_is_ready',
        error: err.message
      });
    }

    // Update task status based on results
    if (taskErrors.length === 0) {
      await updateTaskStatus(task.id, 'completed', {
        message: `Successfully created ${tasks.length} child tasks for OSL id=${oslId}`,
        osl_id: oslId,
        tasks: tasks,
        success: true
      });
    } else if (tasks.length === 0) {
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to create any child tasks.",
        osl_id: oslId,
        errors: taskErrors
      });
    } else {
      await updateTaskStatus(task.id, 'completed', {
        message: `Partially successful: Created ${tasks.length} of 4 tasks, failed to create ${taskErrors.length} tasks`,
        osl_id: oslId,
        tasks: tasks,
        errors: taskErrors,
        partial_success: true
      });
    }
  } catch (err) {
    const errMsg = `[processOslInsertedTask.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process OSL inserted task due to an unexpected error.",
      error: err.message
    });
  }
}
