// check_test_result.js
// Check the result of the test task to see if error extraction worked

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkTestResult() {
  try {
    console.log('Checking result of test task 177928...');
    
    const { data: task, error } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('id', 177928)
      .single();
    
    if (error) {
      console.error('Error fetching task:', error);
      return;
    }
    
    console.log('Task Status:', task.status);
    console.log('Processed At:', task.processed_at);
    
    if (task.result) {
      const result = typeof task.result === 'string' ? JSON.parse(task.result) : task.result;
      console.log('\n📋 Task Result:');
      console.log('Message:', result.message);
      console.log('Error:', result.error);
      
      if (result.error && result.error.includes('MANUAL FIX REQUIRED')) {
        console.log('\n✅ SUCCESS! The MANUAL FIX REQUIRED error was properly extracted!');
      } else if (result.error === 'Unknown error') {
        console.log('\n❌ FAILED: Still showing "Unknown error" - extraction not working');
      } else {
        console.log('\n⚠️  Different error extracted:', result.error);
      }
      
      // Show additional details
      if (result.exit_code) console.log('Exit Code:', result.exit_code);
      if (result.stdout_length) console.log('Stdout Length:', result.stdout_length);
      if (result.stderr_length) console.log('Stderr Length:', result.stderr_length);
    } else {
      console.log('No result found');
    }
    
  } catch (error) {
    console.error('Unexpected error:', error.message);
  }
}

checkTestResult();
